# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

# yaml-language-server: $schema=https://raw.githubusercontent.com/defenseunicorns/zarf/main/zarf.schema.json
kind: ZarfPackageConfig
metadata:
  name: eck-elasticsearch-local
  description: "Elasticsearch Local Manifests Package"
  version: "dev"

components:
  - name: eck-elasticsearch-local
    required: true
    manifests:
      - name: elasticsearch
        namespace: elasticsearch
        files:
          - ./manifests/elasticsearch.yaml
    images:
      - docker.elastic.co/elasticsearch/elasticsearch:8.17.4
      - docker.elastic.co/kibana/kibana:8.17.4
      - docker.elastic.co/logstash/logstash:8.17.4
    actions:
      onDeploy:
        after:
          - description: Validate elasticsearch CR
            maxTotalSeconds: 300
            wait:
              cluster:
                kind: elasticsearches.elasticsearch.k8s.elastic.co
                name: eck-elasticsearch
                namespace: elasticsearch
                condition: "'{.status.phase}'=Ready"