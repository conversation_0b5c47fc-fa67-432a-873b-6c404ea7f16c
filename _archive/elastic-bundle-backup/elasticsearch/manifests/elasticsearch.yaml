apiVersion: elasticsearch.k8s.elastic.co/v1
kind: Elasticsearch
metadata:
  name: eck-elasticsearch
  namespace: elasticsearch
spec:
  image: docker.elastic.co/elasticsearch/elasticsearch:8.17.4
  nodeSets:
  - config:
      node.roles:
      - master
      - ingest
    count: 1
    name: masters
    podTemplate:
      spec:
        containers: null
    volumeClaimTemplates:
    - metadata:
        name: elasticsearch-data
      spec:
        accessModes:
        - ReadWriteOnce
        resources:
          requests:
            storage: 50Gi
  - config:
      node.roles:
      - data
    count: 2
    name: data
    volumeClaimTemplates:
    - metadata:
        name: elasticsearch-data
      spec:
        accessModes:
        - ReadWriteOnce
        resources:
          requests:
            storage: 100Gi
  version: 8.17.4