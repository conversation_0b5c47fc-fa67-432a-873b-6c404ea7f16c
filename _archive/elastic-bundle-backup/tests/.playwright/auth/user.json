{"cookies": [{"name": "AUTH_SESSION_ID", "value": "NmE3MGExODktMTIzZi00OTBiLWEzOGYtYzkwN2I1Y2IzYTU3LmJkZTlOQzZfYXk1ZFBiOVpsdC1feGZWVUYxeEt5X1Q3c0JibUVoZVZsQVJZSjZsS2wyaTF6OU5PNTVuaFZIU1ZiZGhvaWdUcHoxUzFHRXUteEpDeXBR", "domain": "sso.uds.dev", "path": "/realms/uds/", "expires": -1, "httpOnly": true, "secure": true, "sameSite": "None"}, {"name": "KC_AUTH_SESSION_HASH", "value": "E5l92qXHaeMFEy9W9XhniuYzE6wDb8jrtsTzk7luhTU", "domain": "sso.uds.dev", "path": "/realms/uds/", "expires": 1749205613.757682, "httpOnly": false, "secure": true, "sameSite": "None"}, {"name": "KEYCLOAK_IDENTITY", "value": "eyJhbGciOiJIUzUxMiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICI4MzFiM2ZhMy03Y2Y0LTQ2YTktYjc0MC0xMmNmYTgyOGM0MDkifQ.eyJleHAiOjE3NDkyNDE1NTQsImlhdCI6MTc0OTIwNTU1NCwianRpIjoiNmVlZTgwNTQtYTY0NC00ODlkLWI2Y2EtMWI5YWQwNTNlMWIwIiwiaXNzIjoiaHR0cHM6Ly9zc28udWRzLmRldi9yZWFsbXMvdWRzIiwic3ViIjoiNTQyNzU1NmMtODg3Zi00YjEyLWJiMGMtYzU0ZDJhMmRkZjVlIiwidHlwIjoiU2VyaWFsaXplZC1JRCIsInNpZCI6IjZhNzBhMTg5LTEyM2YtNDkwYi1hMzhmLWM5MDdiNWNiM2E1NyIsInN0YXRlX2NoZWNrZXIiOiJKbno1UUxNSkdpS0trdG80ZS1rMlhaQUkzbVBRVUdVMVVWZGZkRnBPT2dVIn0.2Rgx_GVfB3h90JliZu_wVATR7fQqsPyIHE_jIO0Cy0SwOMC7gbyRHnCfig_c9QUbxq4csQgxpyOjLL4zOQGSCg", "domain": "sso.uds.dev", "path": "/realms/uds/", "expires": -1, "httpOnly": true, "secure": true, "sameSite": "None"}, {"name": "KEYCLOAK_SESSION", "value": "E5l92qXHaeMFEy9W9XhniuYzE6wDb8jrtsTzk7luhTU", "domain": "sso.uds.dev", "path": "/realms/uds/", "expires": 1749241554.839649, "httpOnly": false, "secure": true, "sameSite": "None"}, {"name": "sid", "value": "Fe26.2**10dddff0be59698352928f237b26cd6c787334a61992ca9ce02a9b3441dcbc39*lG-jvEAqgwwnEfFnfU2Qfg*vOkwIWzJPlskDdYC-8lLalozPU_zptwvEjdE39FyLr2FCl2UmtMWlOAasbb6FSYZh3BhHCNNQHXJ_MZTLKjUYyYpVmMMd8uOj_d3o8DUrKcNbJf28G7tgDhRqI-eXhPAdFwuJDmKx0ZT8pBc_Magk9IojII3iKdtoBjK8v0bfVgUELXCG6CBSHByLI88s_UMxUFqWrTBtp6bEFhbh7bxhpZtGdNccbd5TUJ_YZtY5lyXqFIy7ArYy0dH70MBIQD0**abb64e668472ffe82a026bbee39de0a4d4059de88ea7b3a4c0d1f1cee4dde1d7*CFmiGP_6CpiNgPyF5uHzKD6iqdi6jCKW_3GjDqy0-xk", "domain": "kibana.uds.dev", "path": "/", "expires": -1, "httpOnly": true, "secure": false, "sameSite": "Lax"}], "origins": [{"origin": "https://kibana.uds.dev", "localStorage": [{"name": "analytics", "value": "{\"reportVersion\":3,\"userAgent\":{\"kibana-user_agent\":{\"key\":\"kibana-user_agent\",\"appName\":\"kibana\",\"type\":\"user_agent\",\"userAgent\":\"Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) HeadlessChrome/136.0.7103.25 Safari/537.36\"}}}"}]}]}