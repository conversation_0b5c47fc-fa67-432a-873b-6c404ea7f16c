# Test info

- Name: home page view
- Location: /app/kibana.test.ts:10:5

# Error details

```
TimeoutError: browserType.launch: Timeout 180000ms exceeded.
Call log:
  - <launching> /ms-playwright/firefox-1482/firefox/firefox -no-remote -headless -profile /tmp/playwright_firefoxdev_profile-dLN3dw -juggler-pipe -silent
  - <launched> pid=503
  - [pid=503][err] *** You are running in headless mode.
  - [pid=503][out] Crash Annotation GraphicsCriticalError: |[0][GFX1-]: glxtest: libpci missing (t=0.394) [GFX1-]: glxtest: libpci missing
  - [pid=503][out] Crash Annotation GraphicsCriticalError: |[0][GFX1-]: glxtest: libpci missing (t=0.394) |[1][GFX1-]: glxtest: Unable to open a connection to the X server (t=0.395) [GFX1-]: glxtest: Unable to open a connection to the X server
  - [pid=503][out] Crash Annotation GraphicsCriticalError: |[0][GFX1-]: glxtest: libpci missing (t=0.394) |[1][GFX1-]: glxtest: Unable to open a connection to the X server (t=0.395) |[2][GFX1-]: No GPUs detected via PCI
  - [pid=503][out]  (t=0.395) [GFX1-]: No GPUs detected via PCI
  - [pid=503][out]
  - [pid=503][err] Unable to revert mtime: /ms-playwright/firefox-1482/firefox/fonts
  5 × [pid=503][err] JavaScript error: resource://gre/modules/XULStore.sys.mjs, line 84: Error: Can't find profile directory.
  - [pid=503][out] Crash Annotation GraphicsCriticalError: |[0][GFX1-]: glxtest: libpci missing (t=0.394) |[1][GFX1-]: glxtest: Unable to open a connection to the X server (t=0.395) |[2][GFX1-]: No GPUs detected via PCI
  - [pid=503][out]  (t=0.395) |[3][GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt (t=0.652) [GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
  - [pid=503][err] [Parent 503, Main Thread] WARNING: unable to create directory '/.cache/dconf': Permission denied.  dconf will not work properly.: 'glib warning', file /home/<USER>/firefox/toolkit/xre/nsSigHandlers.cpp:201
  - [pid=503][err]
  - [pid=503][err] (firefox-default:503): dconf-CRITICAL **: 10:27:10.371: unable to create directory '/.cache/dconf': Permission denied.  dconf will not work properly.
  3 × [pid=503][err] [Parent 503, Main Thread] WARNING: unable to create directory '/.cache/dconf': Permission denied.  dconf will not work properly.: 'glib warning', file /home/<USER>/firefox/toolkit/xre/nsSigHandlers.cpp:201
    - [pid=503][err]
    - [pid=503][err] (firefox-default:503): dconf-CRITICAL **: 10:27:10.372: unable to create directory '/.cache/dconf': Permission denied.  dconf will not work properly.
  - [pid=503][err] [Parent 503, Unnamed thread ffffa1c03ee0] WARNING: unable to create directory '/.cache/dconf': Permission denied.  dconf will not work properly.: 'glib warning', file /home/<USER>/firefox/toolkit/xre/nsSigHandlers.cpp:201
  - [pid=503][err]
  - [pid=503][err] (firefox-default:503): dconf-CRITICAL **: 10:27:10.374: unable to create directory '/.cache/dconf': Permission denied.  dconf will not work properly.
  4 × [pid=503][err] [Parent 503, Main Thread] WARNING: unable to create directory '/.cache/dconf': Permission denied.  dconf will not work properly.: 'glib warning', file /home/<USER>/firefox/toolkit/xre/nsSigHandlers.cpp:201
    - [pid=503][err]
    - [pid=503][err] (firefox-default:503): dconf-CRITICAL **: 10:27:10.476: unable to create directory '/.cache/dconf': Permission denied.  dconf will not work properly.
  4 × [pid=503][err] [Parent 503, Main Thread] WARNING: unable to create directory '/.cache/dconf': Permission denied.  dconf will not work properly.: 'glib warning', file /home/<USER>/firefox/toolkit/xre/nsSigHandlers.cpp:201
    - [pid=503][err]
    - [pid=503][err] (firefox-default:503): dconf-CRITICAL **: 10:27:11.479: unable to create directory '/.cache/dconf': Permission denied.  dconf will not work properly.
  - [pid=503][err] [Parent 503, Main Thread] WARNING: unable to create directory '/.cache/dconf': Permission denied.  dconf will not work properly.: 'glib warning', file /home/<USER>/firefox/toolkit/xre/nsSigHandlers.cpp:201
  - [pid=503][err]
  - [pid=503][err] (firefox-default:503): dconf-CRITICAL **: 10:27:13.491: unable to create directory '/.cache/dconf': Permission denied.  dconf will not work properly.
  2 × [pid=503][err] [Parent 503, Main Thread] WARNING: unable to create directory '/.cache/dconf': Permission denied.  dconf will not work properly.: 'glib warning', file /home/<USER>/firefox/toolkit/xre/nsSigHandlers.cpp:201
    - [pid=503][err]
    - [pid=503][err] (firefox-default:503): dconf-CRITICAL **: 10:28:17.487: unable to create directory '/.cache/dconf': Permission denied.  dconf will not work properly.
  4 × [pid=503][err] [Parent 503, Main Thread] WARNING: unable to create directory '/.cache/dconf': Permission denied.  dconf will not work properly.: 'glib warning', file /home/<USER>/firefox/toolkit/xre/nsSigHandlers.cpp:201
    - [pid=503][err]
    - [pid=503][err] (firefox-default:503): dconf-CRITICAL **: 10:28:17.514: unable to create directory '/.cache/dconf': Permission denied.  dconf will not work properly.

```

# Test source

```ts
   1 | /**
   2 |  * Copyright 2024 Defense Unicorns
   3 |  * SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial
   4 |  */
   5 |
   6 | import { test, expect } from "@playwright/test";
   7 |
   8 | // Customize tests for application being tested. Example given for reference
   9 |
> 10 | test('home page view', async ({ page }) => {
     |     ^ TimeoutError: browserType.launch: Timeout 180000ms exceeded.
  11 |   await page.goto('/');
  12 |
  13 |   await expect(page).toHaveURL(`/app/home`);
  14 |
  15 | });
  16 |
```