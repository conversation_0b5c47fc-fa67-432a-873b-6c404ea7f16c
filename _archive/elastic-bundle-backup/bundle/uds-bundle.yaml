kind: UDSBundle
metadata:
  name: elastic-stack
  description: ELK Bundle
  architecture: amd64
  version: 0.1.0

packages:
  - name: eck-operator
    repository: ghcr.io/uds-packages/eck-operator
    ref: 3.0.0-uds.3-upstream
    overrides:
      eck-operator:
        uds-eck-operator-config:
          values:
            - path: "enterprise.trial"
              value: true
            - path: "ambient"
              value: false
            - path: "additionalNetworkAllow"
              value: 
                - direction: Egress
                  remoteSelector:
                    common.k8s.elastic.co/type: elasticsearch
                  remoteNamespace: elasticsearch
                  port: 9200
                  description: Elasticsearch

  - name: eck-elasticsearch-local
    path: ../elasticsearch
    ref: dev

  - name: eck-elasticsearch
    path: ../../elasticsearch
    ref: dev
    overrides:
      eck-elasticsearch-config:
        uds-eck-elasticsearch-config:
          values:
            - path: "ambient"
              value: false
            - path: "expose.enabled"
              value: true
            - path: "sso.enabled"
              value: true
            - path: "sso"
              value:
                roleMappings:
                  admin:
                    enabled: true
                    roles:
                      - superuser
                    rules:
                      field:
                        username: '*'
            - path: "additionalNetworkAllow"
              value: 
                - direction: Ingress
                  remoteSelector:
                    common.k8s.elastic.co/type: kibana
                  remoteNamespace: kibana
                  port: 9200
                  description: Kibana
                - direction: Ingress
                  remoteSelector:
                    common.k8s.elastic.co/type: logstash
                  remoteNamespace: logstash
                  port: 9200
                  description: Logstash

  - name: kibana
    path: ../../kibana
    ref: dev
    overrides:
      kibana:
        uds-kibana-config:
          values:
            - path: "sso.enabled"
              value: true
            - path: "udsAmbient"
              value: false
            - path: "ambient"
              value: false
        eck-kibana:
          values:
            - path: "version"
              value: 8.17.4
            - path: "image"
              value: registry1.dso.mil/ironbank/elastic/kibana/kibana:8.17.4

  # TODO: placeholder for custom logstash pipeline secret

  - name: logstash
    path: ../../logstash
    ref: dev
    overrides:
      logstash:
        eck-logstash:
          values:
            - path: "version"
              value: 8.17.4
            - path: "image"
              value: registry1.dso.mil/ironbank/elastic/logstash/logstash:8.17.4

        uds-logstash-config:
          values:
            - path: "externalService.enabled"
              value: true
            - path: "ambient"
              value: false
            - path: additionalNetworkAllow
              value:
                - direction: Egress
                  remoteGenerated: Anywhere
              
  # - name: eck-stack-manager
  #   path: ../../eck-stack-manager
  #   ref: dev