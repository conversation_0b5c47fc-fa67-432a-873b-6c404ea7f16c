#!/usr/bin/env bash
# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

# Configuration
NAMESPACE="logstash"
LOGSTASH_URL="https://logstash.uds.dev"
USER="elastic"
PASSWORD=$(uds zarf tools kubectl get secret -n elasticsearch eck-elasticsearch-es-elastic-user -o go-template='{{.data.elastic | base64decode }}')

# Push data to Logstash
push_data_to_logstash() {
    echo "INFO - Pushing data to Logstash"
    RESPONSE=$(curl -s -X POST -k "$LOGSTASH_URL" -u "$USER:$PASSWORD" -H "Content-Type: application/json" -d '{"message": "test"}')
    if [[ "$RESPONSE" == *"ok"* ]]; then
        echo "INFO - Data pushed successfully."
    else
        echo "ERROR - Failed to push data: $RESPONSE"
        exit 1
    fi
}

# Check Elasticsearch index for expected number of records
check_elasticsearch_records() {
    local expected_count=$1
    echo "INFO - Validating data in Elasticsearch index"

    # Get the current date for index naming
    index_date=$(date +"%Y.%m.%d")
    index_name="logstash-${index_date}"

    # Run curl from the Logstash pod to fetch index content
    RESPONSE=$(uds zarf tools kubectl exec -n "$NAMESPACE" -i eck-logstash-ls-0 -- \
        curl -s -u "$USER:$PASSWORD" "https://eck-elasticsearch-es-http.elasticsearch.svc:9200/$index_name/_count" -k 2>/dev/null | tr -d '\r\n')

    # Extract the count from the JSON response
    record_count=$(echo "$RESPONSE" | jq -r '.count')

    if [[ "$record_count" -eq "$expected_count" ]]; then
        echo "INFO - Found expected number of records ($expected_count) in index $index_name."
    else
        echo "ERROR - Expected $expected_count records, but found $record_count in index $index_name."
        exit 1
    fi
}

# Main function
main() {
    local is_upgrade=false

    # Check for --upgrade flag
    while [[ $# -gt 0 ]]; do
        case $1 in
            --upgrade)
                is_upgrade=true
                shift
                ;;
            *)
                echo "ERROR - Unknown argument: $1"
                echo "Usage: $0 [--upgrade]"
                exit 1
                ;;
        esac
    done

    push_data_to_logstash

    if [[ "$is_upgrade" == true ]]; then
        check_elasticsearch_records 2
    else
        check_elasticsearch_records 1
    fi
}

# Run the script
main "$@"
