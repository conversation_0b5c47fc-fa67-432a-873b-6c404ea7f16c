kind: UDSBundle
metadata:
  name: elastic-stack
  description: ELK Bundle
  architecture: amd64
  version: 0.1.0

packages:
  - name: eck-operator
    repository: ghcr.io/uds-packages/eck-operator
    ref: 3.0.0-uds.3-upstream
    overrides:
      eck-operator:
        uds-eck-operator-config:
          values:
            - path: "enterprise.trial"
              value: true
            - path: "additionalNetworkAllow"
              value: 
                - direction: Egress
                  remoteSelector:
                    common.k8s.elastic.co/type: elasticsearch
                  remoteNamespace: elasticsearch
                  port: 9200
                  description: Elasticsearch

  - name: eck-elasticsearch
    path: ../../elasticsearch
    ref: dev
    overrides:
      eck-elasticsearch:
        eck-elasticsearch:
          values:
            - path: "nodeSets"
              value:
                - name: masters
                  count: 1
                  config:
                    node.roles: ["master", "ingest"]
                  volumeClaimTemplates:
                  - metadata:
                      name: elasticsearch-data
                    spec:
                      accessModes:
                      - ReadWriteOnce
                      resources:
                        requests:
                          storage: 50Gi
                - name: data
                  count: 2
                  config:
                    node.roles: ["data"]
                  volumeClaimTemplates:
                  - metadata:
                      name: elasticsearch-data
                    spec:
                      accessModes:
                      - ReadWriteOnce
                      resources:
                        requests:
                          storage: 100Gi 
        uds-eck-elasticsearch-config:
          values:
            - path: "sso.enabled"
              value: true
            - path: "expose.enabled"
              value: true
            - path: "sso"
              value:
                roleMappings:
                  admin:
                    enabled: true
                    roles:
                      - superuser
                    rules:
                      field:
                        username: '*'
            - path: "additionalNetworkAllow"
              value: 
                - direction: Ingress
                  remoteSelector:
                    common.k8s.elastic.co/type: kibana
                  remoteNamespace: kibana
                  port: 9200
                  description: Kibana
                - direction: Ingress
                  remoteSelector:
                    common.k8s.elastic.co/type: logstash
                  remoteNamespace: logstash
                  port: 9200
                  description: Logstash

  - name: kibana
    path: ../../kibana
    ref: dev
    overrides:
      kibana:
        uds-kibana-config:
          values:
            - path: "sso.enabled"
              value: true

  # TODO: placeholder for custom logstash pipeline secret

  - name: logstash
    path: ../../logstash
    ref: dev
    overrides:
      logstash:
        uds-logstash-config:
          values:
            - path: "externalService.enabled"
              value: true
            - path: additionalNetworkAllow
              value:
                - direction: Egress
                  remoteGenerated: Anywhere
              
  # - name: eck-stack-manager
  #   path: ../../eck-stack-manager
  #   ref: dev