# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

tasks:
  - name: all
    actions:
      - task: kibana-ingress
      - task: kibana-ui
      - task: log-shipping

  # using the readiness check endpoint for status check
  - name: kibana-ingress
    actions:
      - description: Kibana UI Status Check
        maxRetries: 30
        cmd: |
          STATUS=$(curl -sL -o /dev/null -w "%{http_code}" https://kibana.uds.dev/)
          echo "kibana system status: ${STATUS}"
          if [ "$STATUS" != "200" ]; then
            sleep 10
            exit 1
          fi

  - name: kibana-ui
    description: Kibana UI Checks
    actions:
      - cmd: |
          docker run --user="$(id -u)" --rm --ipc=host --net=host --mount type=bind,source="$(pwd)",target=/app mcr.microsoft.com/playwright:v1.52.0-jammy sh -c " \
            export npm_config_cache=/tmp/.npm && \
            cd app && \
            npm ci && \
            npx playwright test \
            "
        dir: tests

  - name: log-shipping
    description: Logstash Log Shipping Checks
    inputs:
      options:
        description: "Additional options to the logstash-test.sh script"
    actions:
      - cmd: ./logstash-test.sh ${{ .inputs.options }}
        dir: tests 
