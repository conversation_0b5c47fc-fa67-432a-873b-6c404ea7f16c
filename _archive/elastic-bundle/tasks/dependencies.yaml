tasks:
  - name: build-deps
    description: "Build the UDS Package for Elastic bundle"
    actions:
      - description: "Build the UDS Package for Elastic bundle"
        cmd: |
          run_zarf_create() {
            pushd $1 > /dev/null
            echo "Running in $(pwd)"
            ./uds run create-dev-package --no-progress
            popd > /dev/null
          }
          run_zarf_create "../eck-operator"
          run_zarf_create "../elasticsearch"
          run_zarf_create "../kibana"
          run_zarf_create "../logstash"
          run_zarf_create "../eck-stack-manager"
