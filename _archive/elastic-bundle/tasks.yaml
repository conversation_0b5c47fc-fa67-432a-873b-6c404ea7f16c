# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

includes:
  - dependencies: ./tasks/dependencies.yaml
  - cluster: ./tasks/cluster.yaml
  - test: ./tasks/test.yaml
  - create: https://raw.githubusercontent.com/defenseunicorns/uds-common/v1.16.0/tasks/create.yaml
  - lint: https://raw.githubusercontent.com/defenseunicorns/uds-common/v1.16.0/tasks/lint.yaml
  - pull: https://raw.githubusercontent.com/defenseunicorns/uds-common/v1.16.0/tasks/pull.yaml
  - deploy: https://raw.githubusercontent.com/defenseunicorns/uds-common/v1.16.0/tasks/deploy.yaml
  - setup: https://raw.githubusercontent.com/defenseunicorns/uds-common/v1.16.0/tasks/setup.yaml
  - actions: https://raw.githubusercontent.com/defenseunicorns/uds-common/v1.16.0/tasks/actions.yaml
  - badge: https://raw.githubusercontent.com/defenseunicorns/uds-common/v1.16.0/tasks/badge.yaml
  - upgrade: https://raw.githubusercontent.com/defenseunicorns/uds-common/v1.16.0/tasks/upgrade.yaml
  - compliance: https://raw.githubusercontent.com/defenseunicorns/uds-common/v1.16.0/tasks/compliance.yaml
  - publish: https://raw.githubusercontent.com/defenseunicorns/uds-common/v1.16.0/tasks/publish.yaml

tasks:
  - name: default
    description: Dev set-up for local development
    actions:
      - task: setup:k3d-test-cluster
        with:
          version: "0.43.0"
      - task: deploy-core-monitoring
      - task: build-deploy
  
  - name: build-deploy
    description: Build and deploy the bundle
    actions:
      - task: build-deps-local
      - task: create:test-bundle
      - task: deploy:test-bundle

  - name: build-deps-local
    description: Build dependencies locally
    actions:
      - task: dependencies:build-deps
  
  - name: deploy-core-monitoring
    description: Deploy core monitoring components and deps
    actions:
      - description: "deploy core metrics"
        cmd: |
          uds zarf package deploy oci://ghcr.io/defenseunicorns/packages/uds/core-metrics-server:0.43.0-upstream --confirm --no-progress
      - description: "deploy core monitoring"
        cmd: |
          uds zarf package deploy oci://ghcr.io/defenseunicorns/packages/uds/core-monitoring:0.43.0-upstream --confirm --no-progress

  - name: test-bundle
    description: Run tests on the cluster
    actions:
      - task: setup:keycloak-user
      - task: test:all