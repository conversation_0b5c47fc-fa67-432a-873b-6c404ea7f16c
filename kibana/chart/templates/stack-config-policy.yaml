# Copyright 2025 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

{{- if .Values.sso.enabled }}
apiVersion: stackconfigpolicy.k8s.elastic.co/v1alpha1
kind: StackConfigPolicy
metadata:
  name: {{ .Release.Name }}-policy
  namespace: {{ .Release.Namespace }}
spec:
  kibana:
    config:
      xpack.security.authc.providers:
        oidc.{{ .Values.sso.realm }}:
          order: 0
          realm: "{{ .Values.sso.realm }}"
        {{- if .Values.sso.includeBasicAuth }}
        basic.basic1:
          order: 1
        {{- end }}
{{- end}}
