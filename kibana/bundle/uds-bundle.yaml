# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

kind: UDSBundle
metadata:
  name: kibana-test
  description: A UDS bundle for deploying Kibana and its dependencies on a development cluster
  version: dev

packages:
  # kibana has a dependency on eck-operator and eck-elasticsearch
  - name: eck-operator
    repository: ghcr.io/uds-packages/eck-operator
    ref: 3.0.0-uds.3-upstream
    overrides:
      eck-operator:
        uds-eck-operator-config:
          # Ambient variable is temporarily exposed to test both sidecar and ambient modes TODO: Remove in future release
          variables:
            - path: ambient
              default: true
              name: ambient
          values:
            - path: "enterprise.trial"
              value: true
            - path: "additionalNetworkAllow"
              value:
                - direction: Egress
                  remoteSelector:
                    common.k8s.elastic.co/type: elasticsearch
                  remoteNamespace: elasticsearch
                  port: 9200
                  description: Elasticsearch

  - name: eck-elasticsearch
    repository: ghcr.io/uds-packages/eck-elasticsearch
    ref: 9.0.3-uds.1-upstream
    overrides:
      eck-elasticsearch-uds-config:
        uds-eck-elasticsearch-config:
          # Ambient variable is temporarily exposed to test both sidecar and ambient modes TODO: Remove in future release
          variables:
            - path: ambient
              default: true
              name: ambient
          values:
            - path: "sso.enabled"
              value: true
            - path: "additionalNetworkAllow"
              value:
                - direction: Ingress
                  remoteSelector:
                    common.k8s.elastic.co/type: kibana
                  remoteNamespace: kibana
                  port: 9200
                  description: Kibana
    optionalComponents:
      - eck-elasticsearch-app

  - name: kibana
    path: ../
    ref: dev
    overrides:
      kibana-uds-config:
        uds-kibana-config:
          # Ambient variable is temporarily exposed to test both sidecar and ambient modes TODO: Remove in future release
          variables:
            - path: ambient
              default: true
              name: ambient
          values:
            - path: "sso.enabled"
              value: true
    optionalComponents:
      - kibana-app
