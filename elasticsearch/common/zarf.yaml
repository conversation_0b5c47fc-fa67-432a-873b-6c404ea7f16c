# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

# yaml-language-server: $schema=https://raw.githubusercontent.com/defenseunicorns/zarf/main/zarf.schema.json
kind: ZarfPackageConfig
metadata:
  name: eck-elasticsearch-common
  description: "UDS ECK Elasticsearch Common Package"

components:
  - name: eck-elasticsearch-app
    required: true
    charts:
      - name: eck-elasticsearch
        version: 0.15.0
        namespace: elasticsearch
        url: https://helm.elastic.co
        valuesFiles:
          - ../values/elasticsearch/common-values.yaml

  - name: eck-elasticsearch
    required: true
    charts:
      - name: prometheus-elasticsearch-exporter
        version: 6.7.3
        namespace: elasticsearch
        url: https://prometheus-community.github.io/helm-charts
        valuesFiles:
          - ../values/prom-exporter/common-values.yaml
      - name: uds-eck-elasticsearch-config
        namespace: elasticsearch
        version: 0.1.0
        localPath: ../chart
    actions:
      onDeploy:
        after:
          - description: Validate Elasticsearch CR
            maxTotalSeconds: 300
            wait:
              cluster:
                kind: elasticsearches.elasticsearch.k8s.elastic.co
                name: eck-elasticsearch
                namespace: elasticsearch
                condition: "'{.status.phase}'=Ready"
