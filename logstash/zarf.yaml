# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

# yaml-language-server: $schema=https://raw.githubusercontent.com/defenseunicorns/zarf/main/zarf.schema.json
kind: ZarfPackageConfig
metadata:
  name: logstash
  description: "UDS Logstash package"
  version: "dev"
  annotations:
    dev.uds.title: Logstash
    dev.uds.tagline: ""
    dev.uds.categories: ""
    dev.uds.keywords: ""
    dev.uds.icon: ""

variables:
  - name: DOMAIN
    default: "uds.dev"
  - name: ELASTICSEARCH_NAME
    default: "eck-elasticsearch"
  - name: ELA<PERSON><PERSON><PERSON>ARCH_NAMESPACE
    default: "elasticsearch"
  - name: PIPELINE_SECRET
    default: "default-logstash-pipelines"

components:
  - name: logstash
    required: true
    description: "Deploy Logstash"
    import:
      path: common
    only:
      flavor: upstream
    charts:
      - name: eck-logstash
        valuesFiles:
          - values/upstream-values.yaml
    images:
      - docker.elastic.co/logstash/logstash:9.0.0

  - name: logstash
    required: true
    description: "Deploy Logstash"
    import:
      path: common
    only:
      flavor: registry1
      cluster:
        architecture: amd64
    charts:
      - name: eck-logstash
        valuesFiles:
          - values/registry1-values.yaml
    images:
      - registry1.dso.mil/ironbank/elastic/logstash/logstash:9.0.0

  - name: logstash
    required: true
    description: "Deploy Logstash"
    import:
      path: common
    only:
      flavor: unicorn
    charts:
      - name: eck-logstash
        valuesFiles:
          - values/unicorn-values.yaml
    images:
      - registry1.dso.mil/ironbank/elastic/logstash/logstash:9.0.0
