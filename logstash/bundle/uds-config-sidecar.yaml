# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

# This config tempoarily exists while we test the sidecar mode of Istio in addition to ambient mode.
# Once we are done testing, this file will be removed. TODO: Remove this file once sidecar mode is deprecated.
variables:
  eck-operator:
    ambient: false
  eck-elasticsearch:
    ambient: false
    elasticsearch_nodesets:
      - name: masters
        count: 1
        config:
          node.roles: ["master"]
        volumeClaimTemplates:
        - metadata:
            name: elasticsearch-data
          spec:
            accessModes:
            - ReadWriteOnce
            resources:
              requests:
                storage: 50Gi
      - name: data
        count: 2
        config:
          node.roles: ["data", "ingest"]
        volumeClaimTemplates:
        - metadata:
            name: elasticsearch-data
          spec:
            accessModes:
            - ReadWriteOnce
            resources:
              requests:
                storage: 100Gi
  logstash:
    ambient: false

